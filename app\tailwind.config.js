const plugin = require("tailwindcss/plugin");

module.exports = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: "class",
  theme: {
    extend: {
      animation: {
        fadeIn: "fadeIn 0.3s ease-in-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0", transform: "translateY(5px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    plugin(function ({ addVariant, addUtilities }) {
      // Custom dark variant
      addVariant("dark", "&:is(.dark *)");

      // Custom utility classes
      addUtilities(
        {
          ".dark-gradient": {
            backgroundImage: "linear-gradient(to bottom, #1A1C20, #08090D)",
          },
          ".border-gradient": {
            backgroundImage: "linear-gradient(to bottom, #4B4D4F, #4B4D4F33)",
          },
          ".pattern": {
            backgroundImage: "url('/pattern.png')",
            backgroundPosition: "top",
            backgroundRepeat: "no-repeat",
          },
          ".blue-gradient-dark": {
            backgroundImage: "linear-gradient(to bottom, #171532, #08090D)",
          },
          ".blue-gradient": {
            backgroundImage: "linear-gradient(to left, #FFFFFF, #CAC5FE)",
          },
          ".flex-center": {
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
        },
        ["responsive"] // optional: makes utilities usable with responsive variants
      );
    }),
  ],
};
